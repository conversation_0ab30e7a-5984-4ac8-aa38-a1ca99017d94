<?php
/**
 * PFG Core Connection Test Button Template
 * 
 * @category   PFG
 * @package    PFG_Core
 * <AUTHOR> Development Team
 * @copyright  Copyright (c) 2025 PFG
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
?>
<button id="<?php echo $this->getHtmlId() ?>" type="button" class="scalable" onclick="pfgCoreTestConnection()">
    <span><span><span><?php echo $this->escapeHtml($this->getButtonLabel()) ?></span></span></span>
</button>

<div id="pfg_core_test_result" style="margin-top: 10px; display: none;">
    <div id="pfg_core_test_message"></div>
</div>

<script type="text/javascript">
//<![CDATA[
function pfgCoreTestConnection() {
    var button = $('<?php echo $this->getHtmlId() ?>');
    var resultDiv = $('pfg_core_test_result');
    var messageDiv = $('pfg_core_test_message');
    
    // Disable button and show loading
    button.disabled = true;
    button.innerHTML = '<span><span><span><?php echo $this->__('Testing...') ?></span></span></span>';
    
    // Get form data
    var formData = {
        'form_key': FORM_KEY,
        'username': $('pfg_core_bitbucket_username') ? $('pfg_core_bitbucket_username').value : '',
        'app_password': $('pfg_core_bitbucket_app_password') ? $('pfg_core_bitbucket_app_password').value : '',
        'workspace': $('pfg_core_bitbucket_workspace') ? $('pfg_core_bitbucket_workspace').value : '',
        'project': $('pfg_core_bitbucket_project') ? $('pfg_core_bitbucket_project').value : ''
    };
    
    new Ajax.Request('<?php echo $this->getAjaxUrl() ?>', {
        method: 'post',
        parameters: formData,
        onSuccess: function(response) {
            try {
                var result = response.responseText.evalJSON();
                
                resultDiv.style.display = 'block';
                
                if (result.success) {
                    messageDiv.className = 'success-msg';
                    messageDiv.innerHTML = '<ul><li><span>' + result.message + '</span></li></ul>';
                } else {
                    messageDiv.className = 'error-msg';
                    messageDiv.innerHTML = '<ul><li><span>' + result.message + '</span></li></ul>';
                }
            } catch (e) {
                messageDiv.className = 'error-msg';
                messageDiv.innerHTML = '<ul><li><span><?php echo $this->__('Invalid response from server') ?></span></li></ul>';
                resultDiv.style.display = 'block';
            }
        },
        onFailure: function() {
            messageDiv.className = 'error-msg';
            messageDiv.innerHTML = '<ul><li><span><?php echo $this->__('Connection test failed') ?></span></li></ul>';
            resultDiv.style.display = 'block';
        },
        onComplete: function() {
            // Re-enable button
            button.disabled = false;
            button.innerHTML = '<span><span><span><?php echo $this->escapeHtml($this->getButtonLabel()) ?></span></span></span>';
        }
    });
}
//]]>
</script>
