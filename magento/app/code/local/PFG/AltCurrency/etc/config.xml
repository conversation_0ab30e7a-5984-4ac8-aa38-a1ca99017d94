<?xml version="1.0"?>
<config>
    <modules>
        <PFG_AltCurrency>
            <version>1.0.0</version>
        </PFG_AltCurrency>
    </modules>

    <global>
        <helpers>
            <altcurrency>
                <class>PFG_AltCurrency_Helper</class>
            </altcurrency>
        </helpers>

        <events>
            <core_block_abstract_to_html_after>
                <observers>
                    <altcurrency_observer>
                        <type>singleton</type>
                        <class>PFG_AltCurrency_Model_Observer</class>
                        <method>appendConvertedPrice</method>
                    </altcurrency_observer>
                </observers>
            </core_block_abstract_to_html_after>
        </events>
    </global>

    <default>
        <altcurrency>
            <settings>
                <symbol>€</symbol>
                <rate>0.51129</rate>
            </settings>
        </altcurrency>
    </default>
</config>

