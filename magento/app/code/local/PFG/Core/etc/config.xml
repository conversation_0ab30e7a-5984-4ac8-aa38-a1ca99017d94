<?xml version="1.0"?>
<!--
/**
 * PFG Core Module Configuration
 * 
 * @category   PFG
 * @package    PFG_Core
 * <AUTHOR> Development Team
 * @copyright  Copyright (c) 2025 PFG
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
-->
<config>
    <modules>
        <PFG_Core>
            <version>1.0.0</version>
        </PFG_Core>
    </modules>
    
    <global>
        <models>
            <pfg_core>
                <class>PFG_Core_Model</class>
                <resourceModel>pfg_core_resource</resourceModel>
            </pfg_core>
            <pfg_core_resource>
                <class>PFG_Core_Model_Resource</class>
                <entities>
                    <backup>
                        <table>pfg_core_backup</table>
                    </backup>
                    <installation>
                        <table>pfg_core_installation</table>
                    </installation>
                </entities>
            </pfg_core_resource>
        </models>
        
        <blocks>
            <pfg_core>
                <class>PFG_Core_Block</class>
            </pfg_core>
        </blocks>
        
        <helpers>
            <pfg_core>
                <class>PFG_Core_Helper</class>
            </pfg_core>
        </helpers>
        
        <resources>
            <pfg_core_setup>
                <setup>
                    <module>PFG_Core</module>
                </setup>
            </pfg_core_setup>
        </resources>
    </global>
    
    <admin>
        <routers>
            <adminhtml>
                <args>
                    <modules>
                        <PFG_Core after="Mage_Adminhtml">PFG_Core_Adminhtml</PFG_Core>
                    </modules>
                </args>
            </adminhtml>
        </routers>
    </admin>
    
    <adminhtml>
        <translate>
            <modules>
                <PFG_Core>
                    <files>
                        <default>PFG_Core.csv</default>
                    </files>
                </PFG_Core>
            </modules>
        </translate>
        
        <layout>
            <updates>
                <pfg_core>
                    <file>pfg_core.xml</file>
                </pfg_core>
            </updates>
        </layout>
        
        <events>
            <controller_action_predispatch_adminhtml>
                <observers>
                    <pfg_core_security>
                        <class>pfg_core/observer</class>
                        <method>validateAdminSecurity</method>
                    </pfg_core_security>
                </observers>
            </controller_action_predispatch_adminhtml>
        </events>
    </adminhtml>
    
    <default>
        <pfg>
            <core>
                <enabled>1</enabled>
                <bitbucket_workspace>pfg</bitbucket_workspace>
                <bitbucket_project>LABS</bitbucket_project>
                <api_timeout>30</api_timeout>
                <backup_retention_days>30</backup_retention_days>
                <log_level>1</log_level>
            </core>
        </pfg>
    </default>
</config>
