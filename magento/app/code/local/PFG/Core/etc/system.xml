<?xml version="1.0"?>
<!--
/**
 * PFG Core System Configuration
 *
 * @category   PFG
 * @package    PFG_Core
 * <AUTHOR> Development Team
 * @copyright  Copyright (c) 2025 PFG
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
-->
<config>
    <tabs>
        <pfg translate="label" module="pfg_core">
            <label>PFG</label>
            <sort_order>200</sort_order>
        </pfg>
    </tabs>

    <sections>
        <pfg translate="label" module="pfg_core">
            <label>PFG Core</label>
            <tab>pfg</tab>
            <frontend_type>text</frontend_type>
            <sort_order>100</sort_order>
            <show_in_default>1</show_in_default>
            <show_in_website>0</show_in_website>
            <show_in_store>0</show_in_store>
            <groups>
                <core translate="label" module="pfg_core">
                    <label>Core Module Management</label>
                    <frontend_type>text</frontend_type>
                    <sort_order>10</sort_order>
                    <show_in_default>1</show_in_default>
                    <show_in_website>0</show_in_website>
                    <show_in_store>0</show_in_store>
                    <expanded>1</expanded>
                    <fields>
                        <enabled translate="label comment" module="pfg_core">
                            <label>Enable PFG Core</label>
                            <comment>Enable automated module management through Bitbucket integration</comment>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>10</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>0</show_in_website>
                            <show_in_store>0</show_in_store>
                        </enabled>

                        <bitbucket_username translate="label comment" module="pfg_core">
                            <label>Bitbucket Username</label>
                            <comment>Your Bitbucket username for API authentication</comment>
                            <frontend_type>text</frontend_type>
                            <sort_order>20</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>0</show_in_website>
                            <show_in_store>0</show_in_store>
                            <depends>
                                <enabled>1</enabled>
                            </depends>
                        </bitbucket_username>

                        <bitbucket_app_password translate="label comment" module="pfg_core">
                            <label>Bitbucket App Password</label>
                            <comment>Bitbucket App Password with repository read permissions</comment>
                            <frontend_type>obscure</frontend_type>
                            <backend_model>adminhtml/system_config_backend_encrypted</backend_model>
                            <sort_order>30</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>0</show_in_website>
                            <show_in_store>0</show_in_store>
                            <depends>
                                <enabled>1</enabled>
                            </depends>
                        </bitbucket_app_password>

                        <bitbucket_workspace translate="label comment" module="pfg_core">
                            <label>Bitbucket Workspace</label>
                            <comment>Bitbucket workspace name (default: pfg)</comment>
                            <frontend_type>text</frontend_type>
                            <sort_order>40</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>0</show_in_website>
                            <show_in_store>0</show_in_store>
                            <depends>
                                <enabled>1</enabled>
                            </depends>
                        </bitbucket_workspace>

                        <bitbucket_project translate="label comment" module="pfg_core">
                            <label>Bitbucket Project Key</label>
                            <comment>Bitbucket project key (default: LABS)</comment>
                            <frontend_type>text</frontend_type>
                            <sort_order>50</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>0</show_in_website>
                            <show_in_store>0</show_in_store>
                            <depends>
                                <enabled>1</enabled>
                            </depends>
                        </bitbucket_project>

                        <connection_test translate="label comment" module="pfg_core">
                            <label>Test Connection</label>
                            <comment>Test Bitbucket API connection and display status</comment>
                            <frontend_type>button</frontend_type>
                            <frontend_model>pfg_core/adminhtml_system_config_button_test</frontend_model>
                            <sort_order>60</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>0</show_in_website>
                            <show_in_store>0</show_in_store>
                            <depends>
                                <enabled>1</enabled>
                            </depends>
                        </connection_test>

                        <connection_status translate="label" module="pfg_core">
                            <label>Connection Status</label>
                            <frontend_type>text</frontend_type>
                            <frontend_model>pfg_core/adminhtml_system_config_status</frontend_model>
                            <sort_order>70</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>0</show_in_website>
                            <show_in_store>0</show_in_store>
                            <depends>
                                <enabled>1</enabled>
                            </depends>
                        </connection_status>

                        <repository_management translate="label comment" module="pfg_core">
                            <label>Repository Management</label>
                            <comment>Browse and manage PFG modules from Bitbucket repositories</comment>
                            <frontend_type>text</frontend_type>
                            <frontend_model>pfg_core/adminhtml_system_config_repository_management</frontend_model>
                            <sort_order>80</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>0</show_in_website>
                            <show_in_store>0</show_in_store>
                            <depends>
                                <enabled>1</enabled>
                            </depends>
                        </repository_management>
                    </fields>
                </core>

                <management translate="label" module="pfg_core">
                    <label>Installation &amp; Backup Management</label>
                    <frontend_type>text</frontend_type>
                    <sort_order>20</sort_order>
                    <show_in_default>1</show_in_default>
                    <show_in_website>0</show_in_website>
                    <show_in_store>0</show_in_store>
                    <fields>
                        <installation_history translate="label comment" module="pfg_core">
                            <label>Installation History</label>
                            <comment>View and manage module installation history with complete removal capabilities</comment>
                            <frontend_type>text</frontend_type>
                            <frontend_model>pfg_core/adminhtml_system_config_installation_history</frontend_model>
                            <sort_order>10</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>0</show_in_website>
                            <show_in_store>0</show_in_store>
                        </installation_history>

                        <backup_management translate="label comment" module="pfg_core">
                            <label>Backup Management</label>
                            <comment>Manage module installation backups - download, restore, and cleanup</comment>
                            <frontend_type>text</frontend_type>
                            <frontend_model>pfg_core/adminhtml_system_config_backup_management</frontend_model>
                            <sort_order>20</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>0</show_in_website>
                            <show_in_store>0</show_in_store>
                        </backup_management>

                        <system_logs translate="label comment" module="pfg_core">
                            <label>System Logs</label>
                            <comment>View PFG Core system logs and audit trail with clear/download functions</comment>
                            <frontend_type>text</frontend_type>
                            <frontend_model>pfg_core/adminhtml_system_config_system_logs</frontend_model>
                            <sort_order>30</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>0</show_in_website>
                            <show_in_store>0</show_in_store>
                        </system_logs>
                    </fields>
                </management>

                <advanced translate="label" module="pfg_core">
                    <label>Advanced Settings</label>
                    <frontend_type>text</frontend_type>
                    <sort_order>30</sort_order>
                    <show_in_default>1</show_in_default>
                    <show_in_website>0</show_in_website>
                    <show_in_store>0</show_in_store>
                    <fields>
                        <api_timeout translate="label comment" module="pfg_core">
                            <label>API Timeout (seconds)</label>
                            <comment>Timeout for Bitbucket API requests (default: 30 seconds)</comment>
                            <frontend_type>text</frontend_type>
                            <validate>validate-number validate-greater-than-zero</validate>
                            <sort_order>10</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>0</show_in_website>
                            <show_in_store>0</show_in_store>
                        </api_timeout>

                        <backup_retention_days translate="label comment" module="pfg_core">
                            <label>Backup Retention (days)</label>
                            <comment>Number of days to keep backup files (default: 30 days)</comment>
                            <frontend_type>text</frontend_type>
                            <validate>validate-number validate-greater-than-zero</validate>
                            <sort_order>20</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>0</show_in_website>
                            <show_in_store>0</show_in_store>
                        </backup_retention_days>

                        <log_level translate="label comment" module="pfg_core">
                            <label>Log Level</label>
                            <comment>Logging verbosity level for PFG Core operations</comment>
                            <frontend_type>select</frontend_type>
                            <source_model>pfg_core/system_config_source_loglevel</source_model>
                            <sort_order>30</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>0</show_in_website>
                            <show_in_store>0</show_in_store>
                        </log_level>

                        <enable_security_scan translate="label comment" module="pfg_core">
                            <label>Enable Security Scanning</label>
                            <comment>Scan module files for potential security threats during installation</comment>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>40</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>0</show_in_website>
                            <show_in_store>0</show_in_store>
                        </enable_security_scan>

                        <auto_backup translate="label comment" module="pfg_core">
                            <label>Automatic Backup</label>
                            <comment>Automatically create backups before module installations/updates</comment>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>50</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>0</show_in_website>
                            <show_in_store>0</show_in_store>
                        </auto_backup>
                    </fields>
                </advanced>
            </groups>
        </pfg>
    </sections>
</config>
