<?xml version="1.0"?>
<!--
/**
 * PFG Analytics Module Configuration
 * 
 * This configuration file defines the module structure, admin routes,
 * menu items, and system configuration for the PFG Analytics module.
 * 
 * @package PFG_Analytics
 * <AUTHOR> Development Team
 */
-->
<config>
    <modules>
        <PFG_Analytics>
            <version>1.0.0</version>
        </PFG_Analytics>
    </modules>

    <global>
        <!-- Define module blocks -->
        <blocks>
            <pfg_analytics>
                <class>PFG_Analytics_Block</class>
            </pfg_analytics>
        </blocks>

        <!-- Model configuration -->
        <models>
            <pfg_analytics>
                <class>PFG_Analytics_Model</class>
            </pfg_analytics>
        </models>

        <!-- Define module helpers -->
        <helpers>
            <pfg_analytics>
                <class>PFG_Analytics_Helper</class>
            </pfg_analytics>
        </helpers>

        <!-- Event observers -->
        <events>
            <!-- Clear cache when configuration is saved -->
            <admin_system_config_changed_section_pfg_analytics>
                <observers>
                    <pfg_analytics_clear_cache>
                        <type>singleton</type>
                        <class>pfg_analytics/observer</class>
                        <method>clearAnalyticsCache</method>
                    </pfg_analytics_clear_cache>
                </observers>
            </admin_system_config_changed_section_pfg_analytics>

            <!-- Clear cache when orders are saved -->
            <sales_order_save_after>
                <observers>
                    <pfg_analytics_clear_cache_order_save>
                        <type>singleton</type>
                        <class>pfg_analytics/observer</class>
                        <method>clearAnalyticsCacheOnOrderSave</method>
                    </pfg_analytics_clear_cache_order_save>
                </observers>
            </sales_order_save_after>

            <!-- Clear cache when orders are deleted -->
            <sales_order_delete_after>
                <observers>
                    <pfg_analytics_clear_cache_order_delete>
                        <type>singleton</type>
                        <class>pfg_analytics/observer</class>
                        <method>clearAnalyticsCacheOnOrderDelete</method>
                    </pfg_analytics_clear_cache_order_delete>
                </observers>
            </sales_order_delete_after>
        </events>


    </global>

    <!-- Admin area configuration -->
    <admin>
        <routers>
            <adminhtml>
                <args>
                    <modules>
                        <PFG_Analytics before="Mage_Adminhtml">PFG_Analytics_Adminhtml</PFG_Analytics>
                    </modules>
                </args>
            </adminhtml>
        </routers>
    </admin>

    <!-- Admin HTML configuration -->
    <adminhtml>
        <!-- Translation files -->
        <translate>
            <modules>
                <PFG_Analytics>
                    <files>
                        <default>PFG_Analytics.csv</default>
                    </files>
                </PFG_Analytics>
            </modules>
        </translate>

        <!-- Layout updates -->
        <layout>
            <updates>
                <pfg_analytics>
                    <file>pfg_analytics.xml</file>
                </pfg_analytics>
            </updates>
        </layout>

        <!-- Admin menu configuration -->
        <menu>
            <!-- Main PFG vendor menu -->
            <pfg_base translate="title" module="pfg_analytics">
                <title>PFG</title>
                <sort_order>100</sort_order>
                <children>
                    <!-- Analytics submenu -->
                    <analytics translate="title" module="pfg_analytics">
                        <title>Analytics</title>
                        <sort_order>10</sort_order>
                        <action>adminhtml/pfg_analytics/index</action>
                    </analytics>
                </children>
            </pfg_base>
        </menu>

        <!-- ACL (Access Control List) configuration -->
        <acl>
            <resources>
                <admin>
                    <children>
                        <pfg translate="title" module="pfg_analytics">
                            <title>PFG</title>
                            <sort_order>80</sort_order>
                            <children>
                                <analytics translate="title" module="pfg_analytics">
                                    <title>Analytics</title>
                                    <sort_order>10</sort_order>
                                </analytics>
                            </children>
                        </pfg>
                        <system>
                            <children>
                                <config>
                                    <children>
                                        <pfg_analytics translate="title" module="pfg_analytics">
                                            <title>PFG Analytics</title>
                                        </pfg_analytics>
                                    </children>
                                </config>
                            </children>
                        </system>
                    </children>
                </admin>
            </resources>
        </acl>
    </adminhtml>

    <!-- Default configuration values -->
    <default>
        <pfg_analytics>
            <general>
                <enabled>1</enabled>
            </general>
            <status_mapping>
                <mappings></mappings>
            </status_mapping>
            <top_selling>
                <ignore_categories>3,76,77</ignore_categories>
                <brand_attribute_code>manufacturer</brand_attribute_code>
                <product_display_format>name</product_display_format>
            </top_selling>
        </pfg_analytics>
    </default>
</config>
