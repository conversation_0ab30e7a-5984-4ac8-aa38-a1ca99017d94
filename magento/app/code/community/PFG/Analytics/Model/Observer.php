<?php
/**
 * PFG Analytics Observer
 *
 * This observer handles cache clearing when configuration changes
 * and other system events that affect analytics data.
 *
 * @package PFG_Analytics
 * <AUTHOR> Development Team
 */

class PFG_Analytics_Model_Observer
{
    /**
     * Clear analytics cache when configuration is saved
     *
     * @param Varien_Event_Observer $observer
     * @return void
     */
    public function clearAnalyticsCache(Varien_Event_Observer $observer)
    {
        try {
            $configData = $observer->getEvent()->getConfigData();
            
            // Check if the saved configuration is related to PFG Analytics
            if ($configData && strpos($configData->getPath(), 'pfg_analytics/') === 0) {
                $cache = Mage::app()->getCache();
                $cache->clean(array(PFG_Analytics_Helper_Data::CACHE_TAG));
                
                Mage::log('PFG Analytics cache cleared due to configuration change: ' . $configData->getPath(), null, 'pfg_analytics.log');
            }
            
        } catch (Exception $e) {
            Mage::logException($e);
        }
    }

    /**
     * Clear analytics cache when orders are saved/updated
     *
     * @param Varien_Event_Observer $observer
     * @return void
     */
    public function clearAnalyticsCacheOnOrderSave(Varien_Event_Observer $observer)
    {
        try {
            // Only clear cache if the order status has changed or it's a new order
            $order = $observer->getEvent()->getOrder();
            
            if ($order && ($order->isObjectNew() || $order->dataHasChangedFor('status'))) {
                $cache = Mage::app()->getCache();
                $cache->clean(array(PFG_Analytics_Helper_Data::CACHE_TAG));
                
                Mage::log('PFG Analytics cache cleared due to order change: ' . $order->getIncrementId(), null, 'pfg_analytics.log');
            }
            
        } catch (Exception $e) {
            Mage::logException($e);
        }
    }

    /**
     * Clear analytics cache when orders are deleted
     *
     * @param Varien_Event_Observer $observer
     * @return void
     */
    public function clearAnalyticsCacheOnOrderDelete(Varien_Event_Observer $observer)
    {
        try {
            $cache = Mage::app()->getCache();
            $cache->clean(array(PFG_Analytics_Helper_Data::CACHE_TAG));
            
            $order = $observer->getEvent()->getOrder();
            if ($order) {
                Mage::log('PFG Analytics cache cleared due to order deletion: ' . $order->getIncrementId(), null, 'pfg_analytics.log');
            }
            
        } catch (Exception $e) {
            Mage::logException($e);
        }
    }

    /**
     * Add analytics cache type to cache management
     *
     * @param Varien_Event_Observer $observer
     * @return void
     */
    public function addAnalyticsCacheType(Varien_Event_Observer $observer)
    {
        try {
            $transport = $observer->getEvent()->getTransport();
            $options = $transport->getOptions();
            
            $options[PFG_Analytics_Helper_Data::CACHE_TAG] = Mage::helper('pfg_analytics')->__('PFG Analytics Data');
            
            $transport->setOptions($options);
            
        } catch (Exception $e) {
            Mage::logException($e);
        }
    }
}
