<?php
/**
 * PFG Analytics Configuration Model
 *
 * This model handles configuration-related operations
 * including status mappings and module settings.
 *
 * @package PFG_Analytics
 * <AUTHOR> Development Team
 */

class PFG_Analytics_Model_Config extends Mage_Core_Model_Abstract
{
    // Configuration paths
    const XML_PATH_ENABLED = 'pfg_analytics/general/enabled';
    const XML_PATH_STATUS_MAPPINGS = 'pfg_analytics/status_mapping/mappings';
    const XML_PATH_DEFAULT_DATE_RANGE = 'pfg_analytics/general/default_date_range';
    const XML_PATH_CACHE_LIFETIME = 'pfg_analytics/general/cache_lifetime';

    /**
     * Check if module is enabled
     *
     * @param mixed $store Store ID or store model
     * @return bool
     */
    public function isEnabled($store = null)
    {
        return Mage::getStoreConfigFlag(self::XML_PATH_ENABLED, $store);
    }

    /**
     * Get status mappings configuration
     *
     * @param mixed $store Store ID or store model
     * @return array Status mappings sorted by priority
     */
    public function getStatusMappings($store = null)
    {
        try {
            $mappings = Mage::getStoreConfig(self::XML_PATH_STATUS_MAPPINGS, $store);
            
            if (!$mappings) {
                return array();
            }

            $unserializedMappings = @unserialize($mappings);
            
            if ($unserializedMappings === false || !is_array($unserializedMappings)) {
                return array();
            }

            // Sort by priority (ascending order - lower numbers first)
            uasort($unserializedMappings, function($a, $b) {
                $priorityA = isset($a['priority']) ? (int)$a['priority'] : PFG_Analytics_Helper_Data::DEFAULT_PRIORITY;
                $priorityB = isset($b['priority']) ? (int)$b['priority'] : PFG_Analytics_Helper_Data::DEFAULT_PRIORITY;
                return $priorityA - $priorityB;
            });

            return $unserializedMappings;
            
        } catch (Exception $e) {
            Mage::logException($e);
            return array();
        }
    }

    /**
     * Get mapped status display name
     *
     * @param string $realStatus Real order status
     * @param mixed $store Store ID or store model
     * @return string Display name for the status
     */
    public function getStatusDisplayName($realStatus, $store = null)
    {
        $mappings = $this->getStatusMappings($store);
        
        foreach ($mappings as $mapping) {
            if (isset($mapping['real_statuses']) && is_array($mapping['real_statuses'])) {
                if (in_array($realStatus, $mapping['real_statuses'])) {
                    return isset($mapping['custom_name']) ? $mapping['custom_name'] : $realStatus;
                }
            }
        }
        
        // Return original status if no mapping found
        return $realStatus;
    }

    /**
     * Get all real statuses for a custom display name
     *
     * @param string $customName Custom display name
     * @param mixed $store Store ID or store model
     * @return array Array of real status codes
     */
    public function getRealStatusesForCustomName($customName, $store = null)
    {
        $mappings = $this->getStatusMappings($store);
        
        foreach ($mappings as $mapping) {
            if (isset($mapping['custom_name']) && $mapping['custom_name'] === $customName) {
                return isset($mapping['real_statuses']) ? $mapping['real_statuses'] : array();
            }
        }
        
        return array();
    }

    /**
     * Get grouped status breakdown using mappings
     *
     * @param array $statusBreakdown Raw status breakdown from database
     * @param mixed $store Store ID or store model
     * @return array Grouped status breakdown
     */
    public function getGroupedStatusBreakdown($statusBreakdown, $store = null)
    {
        try {
            $mappings = $this->getStatusMappings($store);
            $groupedBreakdown = array();
            $unmappedStatuses = $statusBreakdown;

            // Process each mapping
            foreach ($mappings as $mapping) {
                if (!isset($mapping['custom_name']) || !isset($mapping['real_statuses'])) {
                    continue;
                }

                $customName = $mapping['custom_name'];
                $realStatuses = $mapping['real_statuses'];
                $totalCount = 0;
                $breakdown = array();

                // Sum up counts for all real statuses in this mapping
                foreach ($realStatuses as $realStatus) {
                    if (isset($statusBreakdown[$realStatus])) {
                        $count = $statusBreakdown[$realStatus];
                        $totalCount += $count;
                        $breakdown[$realStatus] = $count;
                        unset($unmappedStatuses[$realStatus]); // Remove from unmapped
                    }
                }

                if ($totalCount > 0) {
                    $groupedBreakdown[$customName] = array(
                        'count' => $totalCount,
                        'real_statuses' => $realStatuses,
                        'breakdown' => $breakdown,
                        'priority' => isset($mapping['priority']) ? $mapping['priority'] : PFG_Analytics_Helper_Data::DEFAULT_PRIORITY
                    );
                }
            }

            // Add any unmapped statuses as individual entries
            foreach ($unmappedStatuses as $status => $count) {
                $groupedBreakdown[$status] = array(
                    'count' => $count,
                    'real_statuses' => array($status),
                    'breakdown' => array($status => $count),
                    'priority' => PFG_Analytics_Helper_Data::DEFAULT_PRIORITY + 1000 // Lower priority for unmapped
                );
            }

            // Sort by priority
            uasort($groupedBreakdown, function($a, $b) {
                return $a['priority'] - $b['priority'];
            });

            return $groupedBreakdown;
            
        } catch (Exception $e) {
            Mage::logException($e);
            return $statusBreakdown; // Return original breakdown on error
        }
    }

    /**
     * Get default date range in days
     *
     * @param mixed $store Store ID or store model
     * @return int Default date range in days
     */
    public function getDefaultDateRange($store = null)
    {
        $range = Mage::getStoreConfig(self::XML_PATH_DEFAULT_DATE_RANGE, $store);
        return $range ? (int)$range : PFG_Analytics_Helper_Data::DEFAULT_DATE_RANGE_DAYS;
    }

    /**
     * Get cache lifetime in seconds
     *
     * @param mixed $store Store ID or store model
     * @return int Cache lifetime in seconds
     */
    public function getCacheLifetime($store = null)
    {
        $lifetime = Mage::getStoreConfig(self::XML_PATH_CACHE_LIFETIME, $store);
        return $lifetime ? (int)$lifetime : PFG_Analytics_Helper_Data::CACHE_LIFETIME;
    }

    /**
     * Validate status mapping configuration
     *
     * @param array $mappings Status mappings to validate
     * @return array Validation result with errors
     */
    public function validateStatusMappings($mappings)
    {
        $errors = array();
        $usedStatuses = array();
        $usedNames = array();

        if (!is_array($mappings)) {
            $errors[] = 'Status mappings must be an array';
            return array('valid' => false, 'errors' => $errors);
        }

        foreach ($mappings as $index => $mapping) {
            if (!is_array($mapping)) {
                $errors[] = "Mapping at index {$index} must be an array";
                continue;
            }

            // Validate custom name
            if (!isset($mapping['custom_name']) || trim($mapping['custom_name']) === '') {
                $errors[] = "Custom name is required for mapping at index {$index}";
            } else {
                $customName = trim($mapping['custom_name']);
                if (in_array($customName, $usedNames)) {
                    $errors[] = "Custom name '{$customName}' is used multiple times";
                } else {
                    $usedNames[] = $customName;
                }
            }

            // Validate real statuses
            if (!isset($mapping['real_statuses']) || !is_array($mapping['real_statuses']) || empty($mapping['real_statuses'])) {
                $errors[] = "At least one real status is required for mapping at index {$index}";
            } else {
                foreach ($mapping['real_statuses'] as $status) {
                    if (in_array($status, $usedStatuses)) {
                        $errors[] = "Status '{$status}' is mapped multiple times";
                    } else {
                        $usedStatuses[] = $status;
                    }
                }
            }

            // Validate priority
            if (isset($mapping['priority'])) {
                $priority = (int)$mapping['priority'];
                if ($priority < 0 || $priority > 9999) {
                    $errors[] = "Priority for mapping at index {$index} must be between 0 and 9999";
                }
            }
        }

        return array(
            'valid' => empty($errors),
            'errors' => $errors
        );
    }


}
