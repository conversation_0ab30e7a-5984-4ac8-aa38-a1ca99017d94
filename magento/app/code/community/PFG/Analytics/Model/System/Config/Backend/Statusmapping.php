<?php
/**
 * PFG Analytics Status Mapping Backend Model
 *
 * This model handles the saving and loading of status mapping configuration
 * data in the system configuration.
 *
 * @package PFG_Analytics
 * <AUTHOR> Development Team
 */

class PFG_Analytics_Model_System_Config_Backend_Statusmapping extends Mage_Core_Model_Config_Data
{
    /**
     * Default priority value for status mappings
     */
    const DEFAULT_PRIORITY = 999;
    /**
     * Process data before saving
     *
     * @return PFG_Analytics_Model_System_Config_Backend_Statusmapping
     */
    protected function _beforeSave()
    {
        try {
            $value = $this->getValue();

            if (is_array($value)) {
                // Clean up the data and remove empty entries
                $cleanedValue = array();
            
            foreach ($value as $row) {
                if (isset($row['custom_name']) && isset($row['real_statuses']) &&
                    !empty(trim($row['custom_name'])) && !empty($row['real_statuses'])) {

                    // Ensure real_statuses is an array
                    if (!is_array($row['real_statuses'])) {
                        $row['real_statuses'] = array($row['real_statuses']);
                    }

                    // Remove empty values from real_statuses
                    $row['real_statuses'] = array_filter($row['real_statuses'], function($status) {
                        return !empty(trim($status));
                    });

                    if (!empty($row['real_statuses'])) {
                        // Get priority value, default to constant if not set or empty
                        $priority = isset($row['priority']) && !empty(trim($row['priority']))
                                  ? (int)trim($row['priority'])
                                  : self::DEFAULT_PRIORITY;

                        // Validate priority range
                        if ($priority < 0 || $priority > 9999) {
                            $priority = self::DEFAULT_PRIORITY;
                        }

                        $cleanedValue[] = array(
                            'priority' => $priority,
                            'custom_name' => trim($row['custom_name']),
                            'real_statuses' => array_values($row['real_statuses'])
                        );
                    }
                }

                // Sort by priority (lower numbers first)
                usort($cleanedValue, function($a, $b) {
                    return $a['priority'] - $b['priority'];
                });
            }

            // Serialize the cleaned data
            $this->setValue(serialize($cleanedValue));
        } else {
            // If not an array, set empty serialized array
            $this->setValue(serialize(array()));
        }

        } catch (Exception $e) {
            Mage::logException($e);
            throw new Exception('Invalid status mapping configuration: ' . $e->getMessage());
        }

        return parent::_beforeSave();
    }

    /**
     * Process data after loading
     *
     * @return PFG_Analytics_Model_System_Config_Backend_Statusmapping
     */
    protected function _afterLoad()
    {
        try {
            $value = $this->getValue();

            if ($value) {
                $unserializedValue = @unserialize($value);
                if ($unserializedValue !== false && is_array($unserializedValue)) {
                    // Sort by priority when loading
                    usort($unserializedValue, function($a, $b) {
                        $priorityA = isset($a['priority']) ? (int)$a['priority'] : self::DEFAULT_PRIORITY;
                        $priorityB = isset($b['priority']) ? (int)$b['priority'] : self::DEFAULT_PRIORITY;
                        return $priorityA - $priorityB;
                    });

                    $this->setValue($unserializedValue);
                } else {
                    $this->setValue(array());
                }
            } else {
                $this->setValue(array());
            }

        } catch (Exception $e) {
            Mage::logException($e);
            $this->setValue(array());
        }

        return parent::_afterLoad();
    }
}
