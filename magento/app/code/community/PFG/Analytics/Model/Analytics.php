<?php
/**
 * PFG Analytics Model - Complete Version
 *
 * This model handles the core analytics data retrieval and calculations
 * using optimized SQL queries instead of collection iterations.
 *
 * @package PFG_Analytics
 * <AUTHOR> Development Team
 */

class PFG_Analytics_Model_Analytics extends Mage_Core_Model_Abstract
{
    // CONSTANTS MOVED TO HELPER CLASS - Use PFG_Analytics_Helper_Data constants instead
    // This eliminates duplicate constants and centralizes configuration

    /**
     * Get sales data for specified period using optimized SQL queries
     *
     * @param string $fromDate Start date in Y-m-d format
     * @param string $toDate End date in Y-m-d format
     * @param array $selectedStatuses Array of order status codes to filter by
     * @return array Sales data including totals and status breakdown
     * @throws Exception If date range is invalid or database error occurs
     */
    public function getSalesDataForPeriod($fromDate, $toDate, $selectedStatuses = array())
    {
        try {
            // Validate date inputs
            if (!$this->_validateDate($fromDate) || !$this->_validateDate($toDate)) {
                throw new Exception('Invalid date format provided');
            }

            $resource = Mage::getSingleton('core/resource');
            $readConnection = $resource->getConnection('core_read');
            $orderTable = $resource->getTableName('sales/order');
            
            // Build base query with aggregation for main metrics
            $select = $readConnection->select()
                ->from($orderTable, array(
                    'total_orders' => 'COUNT(*)',
                    'total_amount' => 'SUM(grand_total)',
                    'average_amount' => 'AVG(grand_total)'
                ))
                ->where('created_at >= ?', $fromDate . ' 00:00:00')
                ->where('created_at <= ?', $toDate . ' 23:59:59');
            
            // Add status filter if specific statuses are selected
            if (!empty($selectedStatuses)) {
                $select->where('status IN (?)', $selectedStatuses);
            }
            
            $result = $readConnection->fetchRow($select);
            
            // Get status breakdown with separate optimized query
            $statusBreakdown = $this->_getStatusBreakdown($fromDate, $toDate, $selectedStatuses);
            
            return array(
                'total_orders' => (int)$result['total_orders'],
                'total_amount' => (float)$result['total_amount'],
                'average_amount' => (float)$result['average_amount'],
                'status_breakdown' => $statusBreakdown
            );
            
        } catch (Exception $e) {
            Mage::logException($e);
            return array(
                'total_orders' => 0,
                'total_amount' => 0,
                'average_amount' => 0,
                'status_breakdown' => array(),
                'error' => 'Unable to load sales data. Please try again.'
            );
        }
    }

    /**
     * Get chart data for specified period using optimized SQL queries
     *
     * @param string $fromDate Start date in Y-m-d format
     * @param string $toDate End date in Y-m-d format
     * @param array $selectedStatuses Array of order status codes to filter by
     * @param string $grouping Grouping option: 'day', 'week', or 'month'
     * @return array Chart data with labels and values
     * @throws Exception If parameters are invalid
     */
    public function getChartDataForPeriod($fromDate, $toDate, $selectedStatuses = array(), $grouping = 'day', $includePurchases = true, $includeRevenue = false)
    {
        try {
            // EDGE CASE: Validate that at least one data type is requested
            if (!$includePurchases && !$includeRevenue) {
                Mage::log('Analytics: No data type requested (both purchases and revenue disabled)', Zend_Log::WARN);
                return array(
                    'labels' => array(),
                    'data' => array(),
                    'revenue' => array(),
                    'from_date' => $fromDate,
                    'to_date' => $toDate,
                    'grouping' => $grouping,
                    'warning' => 'No data type selected for display'
                );
            }

            // Validate inputs
            if (!$this->_validateDate($fromDate) || !$this->_validateDate($toDate)) {
                throw new Exception('Invalid date format provided');
            }

            // EDGE CASE: Validate date range logic
            if (strtotime($fromDate) > strtotime($toDate)) {
                throw new Exception('Start date cannot be after end date');
            }

            // Validate date range (max 1 year)
            $fromDateTime = new DateTime($fromDate);
            $toDateTime = new DateTime($toDate);
            $interval = $fromDateTime->diff($toDateTime);

            if ($interval->days > PFG_Analytics_Helper_Data::MAX_CHART_DAYS) {
                // Limit to 1 year from the from_date
                $toDateTime = clone $fromDateTime;
                $toDateTime->add(new DateInterval('P365D'));
                $toDate = $toDateTime->format(PFG_Analytics_Helper_Data::DATE_FORMAT_INPUT);
                Mage::log('Analytics: Date range limited to ' . PFG_Analytics_Helper_Data::MAX_CHART_DAYS . ' days', Zend_Log::INFO);
            }

            // EDGE CASE: Validate grouping parameter
            $validGroupings = array('day', 'week', 'month');
            if (!in_array($grouping, $validGroupings)) {
                Mage::log('Analytics: Invalid grouping parameter: ' . $grouping, Zend_Log::WARN);
                $grouping = 'day'; // Default fallback
            }

            $resource = Mage::getSingleton('core/resource');
            $readConnection = $resource->getConnection('core_read');
            $orderTable = $resource->getTableName('sales/order');
            
            // Use SQL DATE functions for efficient grouping
            $dateFormat = $this->_getSqlDateFormat($grouping);
            
            // Build select fields based on what data is requested
            $selectFields = array(
                'period' => new Zend_Db_Expr("DATE_FORMAT(created_at, '$dateFormat')")
            );

            if ($includePurchases) {
                $selectFields['count'] = 'COUNT(*)';
            }

            if ($includeRevenue) {
                $selectFields['revenue'] = 'SUM(grand_total)';
            }

            $select = $readConnection->select()
                ->from($orderTable, $selectFields)
                ->where('created_at >= ?', $fromDate . ' 00:00:00')
                ->where('created_at <= ?', $toDate . ' 23:59:59')
                ->group('period')
                ->order('period ASC');
                
            if (!empty($selectedStatuses)) {
                $select->where('status IN (?)', $selectedStatuses);
            }
            
            if ($includePurchases || $includeRevenue) {
                // Fetch all data when multiple metrics are requested
                $results = $readConnection->fetchAll($select);

                // Separate data by type
                $countData = array();
                $revenueData = array();

                foreach ($results as $row) {
                    if ($includePurchases && isset($row['count'])) {
                        $countData[$row['period']] = $row['count'];
                    }
                    if ($includeRevenue && isset($row['revenue'])) {
                        $revenueData[$row['period']] = floatval($row['revenue']);
                    }
                }

                // Fill in missing periods with zero values
                $returnData = array(
                    'from_date' => $fromDate,
                    'to_date' => $toDate,
                    'grouping' => $grouping
                );

                if ($includePurchases) {
                    $filledCountResults = $this->_fillMissingPeriods($countData, $fromDate, $toDate, $grouping);
                    $returnData['labels'] = array_keys($filledCountResults);
                    $returnData['data'] = array_values($filledCountResults);
                }

                if ($includeRevenue) {
                    $filledRevenueResults = $this->_fillMissingPeriods($revenueData, $fromDate, $toDate, $grouping);
                    if (!isset($returnData['labels'])) {
                        $returnData['labels'] = array_keys($filledRevenueResults);
                    }
                    $returnData['revenue'] = array_values($filledRevenueResults);
                }

                return $returnData;
            } else {
                // No data requested - return empty structure
                return array(
                    'labels' => array(),
                    'data' => array(),
                    'from_date' => $fromDate,
                    'to_date' => $toDate,
                    'grouping' => $grouping
                );
            }
            
        } catch (Exception $e) {
            Mage::logException($e);
            return array(
                'labels' => array(),
                'data' => array(),
                'from_date' => $fromDate,
                'to_date' => $toDate,
                'grouping' => $grouping,
                'error' => 'Unable to load chart data. Please try again.'
            );
        }
    }

    /**
     * Get status breakdown using optimized SQL query
     *
     * @param string $fromDate Start date
     * @param string $toDate End date
     * @param array $selectedStatuses Selected statuses filter
     * @return array Status breakdown with counts
     */
    protected function _getStatusBreakdown($fromDate, $toDate, $selectedStatuses = array())
    {
        try {
            $resource = Mage::getSingleton('core/resource');
            $readConnection = $resource->getConnection('core_read');
            $orderTable = $resource->getTableName('sales/order');

            $statusSelect = $readConnection->select()
                ->from($orderTable, array('status', 'count' => 'COUNT(*)'))
                ->where('created_at >= ?', $fromDate . ' 00:00:00')
                ->where('created_at <= ?', $toDate . ' 23:59:59')
                ->group('status')
                ->order('count DESC');

            if (!empty($selectedStatuses)) {
                $statusSelect->where('status IN (?)', $selectedStatuses);
            }

            $result = $readConnection->fetchPairs($statusSelect);

            // Log successful query for debugging
            if (empty($result)) {
                Mage::log('Analytics: No status breakdown data found for date range ' . $fromDate . ' to ' . $toDate, Zend_Log::INFO);
            }

            return $result;

        } catch (Exception $e) {
            Mage::logException($e);
            Mage::log('Analytics: Failed to retrieve status breakdown data: ' . $e->getMessage(), Zend_Log::ERR);
            return array();
        }
    }

    /**
     * Get SQL date format string for grouping
     *
     * @param string $grouping Grouping type
     * @return string SQL date format
     */
    protected function _getSqlDateFormat($grouping)
    {
        switch ($grouping) {
            case 'week':
                return '%Y-W%u'; // Year-Week format
            case 'month':
                return '%Y-%m'; // Year-Month format
            default:
                return '%Y-%m-%d'; // Year-Month-Day format
        }
    }

    /**
     * Fill missing periods with zero values
     *
     * @param array $results Existing results from database
     * @param string $fromDate Start date
     * @param string $toDate End date
     * @param string $grouping Grouping type
     * @return array Complete results with zero-filled gaps
     */
    protected function _fillMissingPeriods($results, $fromDate, $toDate, $grouping)
    {
        $filledResults = array();
        $currentDate = new DateTime($fromDate);
        $endDate = new DateTime($toDate);
        
        while ($currentDate <= $endDate) {
            $periodKey = $this->_getGroupingKey($currentDate, $grouping);
            $filledResults[$periodKey] = isset($results[$periodKey]) ? (int)$results[$periodKey] : 0;
            
            // Move to next period
            switch ($grouping) {
                case 'week':
                    $currentDate->add(new DateInterval('P7D'));
                    break;
                case 'month':
                    $currentDate->add(new DateInterval('P1M'));
                    break;
                default: // day
                    $currentDate->add(new DateInterval('P1D'));
                    break;
            }
        }
        
        return $filledResults;
    }

    /**
     * Get grouping key for a date
     *
     * @param DateTime $date Date object
     * @param string $grouping Grouping type
     * @return string Grouping key
     */
    protected function _getGroupingKey($date, $grouping)
    {
        switch ($grouping) {
            case 'week':
                return $date->format('Y-\WW');
            case 'month':
                return $date->format('Y-m');
            default:
                return $date->format('Y-m-d');
        }
    }

    /**
     * Validate date format
     *
     * @param string $date Date string to validate
     * @return bool True if valid, false otherwise
     */
    protected function _validateDate($date)
    {
        $d = DateTime::createFromFormat(PFG_Analytics_Helper_Data::DATE_FORMAT_INPUT, $date);
        return $d && $d->format(PFG_Analytics_Helper_Data::DATE_FORMAT_INPUT) === $date;
    }

    /**
     * Calculate percentage change between two values
     *
     * @param float $oldValue Previous value
     * @param float $newValue Current value
     * @return array Array with percentage and direction
     */
    public function calculatePercentageChange($oldValue, $newValue)
    {
        if ($oldValue == 0) {
            if ($newValue > 0) {
                return array('percentage' => 100, 'direction' => 'up', 'is_new' => true);
            } else {
                return array('percentage' => 0, 'direction' => 'neutral', 'is_new' => false);
            }
        }

        $change = (($newValue - $oldValue) / $oldValue) * 100;
        $direction = $change > 0 ? 'up' : ($change < 0 ? 'down' : 'neutral');

        return array(
            'percentage' => abs(round($change, 1)),
            'direction' => $direction,
            'is_new' => false
        );
    }

    /**
     * Get top selling brands data for specified period
     * Shows top 10 individually, then groups the rest as "Other"
     *
     * @param string $fromDate Start date in Y-m-d format
     * @param string $toDate End date in Y-m-d format
     * @param array $selectedStatuses Array of order status codes to filter by
     * @return array Top selling brands data with "Other" grouping
     */
    public function getTopSellingBrandsData($fromDate, $toDate, $selectedStatuses = array())
    {
        try {
            // Validate date inputs
            if (!$this->_validateDate($fromDate) || !$this->_validateDate($toDate)) {
                throw new Exception('Invalid date format provided');
            }

            // Get brand attribute code from configuration
            $brandAttributeCode = Mage::getStoreConfig('pfg_analytics/top_selling/brand_attribute_code');

            if (empty($brandAttributeCode)) {
                return array(); // No brand attribute configured
            }

            $resource = Mage::getSingleton('core/resource');
            $readConnection = $resource->getConnection('core_read');

            // Get the attribute details
            $attributeSelect = $readConnection->select()
                ->from(array('ea' => $resource->getTableName('eav/attribute')), array('attribute_id', 'backend_type'))
                ->where('ea.attribute_code = ?', $brandAttributeCode)
                ->where('ea.entity_type_id = ?', 4); // Product entity type

            $attributeInfo = $readConnection->fetchRow($attributeSelect);

            if (!$attributeInfo) {
                throw new Exception('Brand attribute not found: ' . $brandAttributeCode);
            }

            $attributeId = $attributeInfo['attribute_id'];
            $backendType = $attributeInfo['backend_type'];

            // Build SQL query for manufacturer attribute (int type with option values)
            // Include revenue calculation by summing order item totals
            $sql = "
                SELECT
                    eaov.value as brand_name,
                    SUM(soi.qty_ordered) as total_quantity,
                    COUNT(DISTINCT so.entity_id) as order_count,
                    SUM(soi.row_total) as total_revenue
                FROM sales_flat_order so
                INNER JOIN sales_flat_order_item soi ON so.entity_id = soi.order_id
                INNER JOIN catalog_product_entity_int pei ON soi.product_id = pei.entity_id
                INNER JOIN eav_attribute_option_value eaov ON pei.value = eaov.option_id
                WHERE so.created_at >= ?
                AND so.created_at <= ?
                AND soi.parent_item_id IS NULL
                AND pei.attribute_id = ?
                AND eaov.store_id = 0
                AND pei.value IS NOT NULL
                AND pei.value != ''
                " . (!empty($selectedStatuses) ? "AND so.status IN ('" . implode("','", $selectedStatuses) . "')" : "") . "
                GROUP BY eaov.value, pei.value
                ORDER BY total_quantity DESC
            ";

            // Prepare parameters
            $params = array(
                $fromDate . ' 00:00:00',
                $toDate . ' 23:59:59',
                $attributeId
            );

            $results = $readConnection->fetchAll($sql, $params);

            // Apply "Other" grouping logic: show top 10, group the rest
            return $this->_applyOtherGrouping($results, 'brand_name');

        } catch (Exception $e) {
            Mage::logException($e);
            return array();
        }
    }

    /**
     * Get top selling categories data for specified period
     * Shows top 10 individually, then groups the rest as "Other"
     *
     * @param string $fromDate Start date in Y-m-d format
     * @param string $toDate End date in Y-m-d format
     * @param array $selectedStatuses Array of order status codes to filter by
     * @return array Top selling categories data with "Other" grouping
     */
    public function getTopSellingCategoriesData($fromDate, $toDate, $selectedStatuses = array())
    {
        try {
            // Validate date inputs
            if (!$this->_validateDate($fromDate) || !$this->_validateDate($toDate)) {
                throw new Exception('Invalid date format provided');
            }

            // Get ignored categories from configuration
            $ignoreCategoriesConfig = Mage::getStoreConfig('pfg_analytics/top_selling/ignore_categories');
            $ignoreCategories = $this->_parseIgnoreCategories($ignoreCategoriesConfig);

            $resource = Mage::getSingleton('core/resource');
            $readConnection = $resource->getConnection('core_read');

            // Build SQL query for top selling categories
            // Each product is counted only once in the first matching category (lowest category_id)
            $sql = "
                SELECT
                    first_category.category_id,
                    first_category.category_name,
                    SUM(order_items.qty_ordered) as total_quantity,
                    COUNT(DISTINCT order_items.order_id) as order_count
                FROM (
                    -- For each order item, find the first matching top-level category
                    SELECT DISTINCT
                        soi.order_id,
                        soi.product_id,
                        soi.qty_ordered,
                        MIN(top_categories.category_id) as first_category_id
                    FROM sales_flat_order so
                    INNER JOIN sales_flat_order_item soi ON so.entity_id = soi.order_id
                    INNER JOIN catalog_category_product ccp ON soi.product_id = ccp.product_id
                    INNER JOIN catalog_category_entity cce ON ccp.category_id = cce.entity_id
                    INNER JOIN (
                        -- Get all top-level categories (level 2) excluding specified IDs
                        SELECT
                            cce2.entity_id as category_id,
                            ccv2.value as category_name
                        FROM catalog_category_entity cce2
                        INNER JOIN catalog_category_entity_varchar ccv2 ON cce2.entity_id = ccv2.entity_id
                        WHERE ccv2.attribute_id = (
                            SELECT attribute_id
                            FROM eav_attribute
                            WHERE attribute_code = 'name' AND entity_type_id = 3
                            LIMIT 1
                        )
                        AND cce2.level = 2  -- Only top-level categories
                        " . (!empty($ignoreCategories) ? "AND cce2.entity_id NOT IN (" . implode(',', $ignoreCategories) . ")" : "") . "
                    ) top_categories ON (
                        -- Match products to top-level categories
                        (cce.level = 2 AND cce.entity_id = top_categories.category_id) OR
                        (cce.level > 2 AND cce.path LIKE CONCAT('1/2/', top_categories.category_id, '/%'))
                    )
                    WHERE so.created_at >= ?
                    AND so.created_at <= ?
                    AND soi.parent_item_id IS NULL  -- Exclude child items of configurable products
                    AND cce.entity_id NOT IN (1, 2" . (!empty($ignoreCategories) ? ", " . implode(',', $ignoreCategories) : "") . ")  -- Exclude root, default, and specified categories
                    " . (!empty($selectedStatuses) ? "AND so.status IN ('" . implode("','", $selectedStatuses) . "')" : "") . "
                    GROUP BY soi.order_id, soi.product_id, soi.qty_ordered
                ) order_items
                INNER JOIN (
                    -- Get category names for the first categories
                    SELECT
                        cce2.entity_id as category_id,
                        ccv2.value as category_name
                    FROM catalog_category_entity cce2
                    INNER JOIN catalog_category_entity_varchar ccv2 ON cce2.entity_id = ccv2.entity_id
                    WHERE ccv2.attribute_id = (
                        SELECT attribute_id
                        FROM eav_attribute
                        WHERE attribute_code = 'name' AND entity_type_id = 3
                        LIMIT 1
                    )
                    AND cce2.level = 2  -- Only top-level categories
                    " . (!empty($ignoreCategories) ? "AND cce2.entity_id NOT IN (" . implode(',', $ignoreCategories) . ")" : "") . "
                ) first_category ON order_items.first_category_id = first_category.category_id
                GROUP BY first_category.category_id, first_category.category_name
                ORDER BY total_quantity DESC
            ";

            // Prepare parameters
            $params = array(
                $fromDate . ' 00:00:00',
                $toDate . ' 23:59:59'
            );

            $results = $readConnection->fetchAll($sql, $params);

            // Apply "Other" grouping logic: show top 10, group the rest
            return $this->_applyOtherGrouping($results, 'category_name');

        } catch (Exception $e) {
            Mage::logException($e);
            return array();
        }
    }

    /**
     * Parse ignore categories configuration string
     *
     * @param string $configValue Comma-separated category IDs
     * @return array Array of integer category IDs
     */
    protected function _parseIgnoreCategories($configValue)
    {
        if (empty($configValue)) {
            return array(3, 76, 77); // Default ignored categories
        }

        $categories = explode(',', $configValue);
        $result = array();

        foreach ($categories as $categoryId) {
            $categoryId = (int)trim($categoryId);
            if ($categoryId > 0) {
                $result[] = $categoryId;
            }
        }

        return empty($result) ? array(3, 76, 77) : $result;
    }

    /**
     * Get top selling products data for specified period
     * Shows top 10 individually, then groups the rest as "Other"
     *
     * @param string $fromDate Start date in Y-m-d format
     * @param string $toDate End date in Y-m-d format
     * @param array $selectedStatuses Array of order status codes to filter by
     * @return array Top selling products data with "Other" grouping
     */
    public function getTopSellingProductsData($fromDate, $toDate, $selectedStatuses = array())
    {
        try {
            // Validate date inputs
            if (!$this->_validateDate($fromDate) || !$this->_validateDate($toDate)) {
                throw new Exception('Invalid date format provided');
            }

            // Get product display format from configuration
            $displayFormat = Mage::getStoreConfig('pfg_analytics/top_selling/product_display_format');
            if (empty($displayFormat)) {
                $displayFormat = 'name_sku'; // Default format
            }

            $resource = Mage::getSingleton('core/resource');
            $readConnection = $resource->getConnection('core_read');

            // Build SQL query for top selling products
            $sql = "
                SELECT
                    p.entity_id as product_id,
                    p.sku,
                    pv_name.value as product_name,
                    SUM(soi.qty_ordered) as total_quantity,
                    COUNT(DISTINCT so.entity_id) as order_count
                FROM sales_flat_order so
                INNER JOIN sales_flat_order_item soi ON so.entity_id = soi.order_id
                INNER JOIN catalog_product_entity p ON soi.product_id = p.entity_id
                LEFT JOIN catalog_product_entity_varchar pv_name ON (
                    p.entity_id = pv_name.entity_id
                    AND pv_name.attribute_id = (
                        SELECT attribute_id
                        FROM eav_attribute
                        WHERE attribute_code = 'name' AND entity_type_id = 4
                        LIMIT 1
                    )
                    AND pv_name.store_id = 0
                )
                WHERE so.created_at >= ?
                AND so.created_at <= ?
                AND soi.parent_item_id IS NULL
                " . (!empty($selectedStatuses) ? "AND so.status IN ('" . implode("','", $selectedStatuses) . "')" : "") . "
                GROUP BY p.entity_id, p.sku, pv_name.value
                ORDER BY total_quantity DESC
            ";

            // Prepare parameters
            $params = array(
                $fromDate . ' 00:00:00',
                $toDate . ' 23:59:59'
            );

            $results = $readConnection->fetchAll($sql, $params);

            // Format the display names
            foreach ($results as &$result) {
                $result['display_name'] = $this->_formatProductDisplay(
                    $displayFormat,
                    $result['product_name'],
                    $result['sku']
                );
            }

            // Apply "Other" grouping logic: show top 10, group the rest
            return $this->_applyOtherGrouping($results, 'display_name');

        } catch (Exception $e) {
            Mage::logException($e);
            return array();
        }
    }

    /**
     * Format product display name based on configuration
     *
     * @param string $format Display format template
     * @param string $name Product name
     * @param string $sku Product SKU
     * @return string Formatted display name
     */
    protected function _formatProductDisplay($format, $name, $sku)
    {
        // Handle simple format options from system configuration
        switch ($format) {
            case 'name':
                return $name;
            case 'sku':
                return $sku;
            case 'name_sku':
                return $name . ' (SKU: ' . $sku . ')';
            default:
                // Handle template format with placeholders (for backward compatibility)
                $replacements = array(
                    '{name}' => $name,
                    '{sku}' => $sku
                );
                return str_replace(array_keys($replacements), array_values($replacements), $format);
        }
    }

    /**
     * Apply "Other" grouping logic to results
     * Shows first 10 results individually, groups the rest as "Other"
     *
     * @param array $results Raw results from database
     * @param string $nameField Field name containing the display name
     * @return array Processed results with "Other" grouping
     */
    protected function _applyOtherGrouping($results, $nameField)
    {
        // If 10 or fewer results, return as-is
        if (count($results) <= 10) {
            return $results;
        }

        // Take first 10 results
        $topResults = array_slice($results, 0, 10);

        // Group remaining results as "Other"
        $otherResults = array_slice($results, 10);

        if (!empty($otherResults)) {
            $otherTotalQuantity = 0;
            $otherOrderCount = 0;
            $otherTotalRevenue = 0;

            foreach ($otherResults as $result) {
                $otherTotalQuantity += (int)$result['total_quantity'];
                $otherOrderCount += (int)$result['order_count'];
                if (isset($result['total_revenue'])) {
                    $otherTotalRevenue += (float)$result['total_revenue'];
                }
            }

            // Create "Other" entry
            $otherEntry = array(
                $nameField => 'Other (' . count($otherResults) . ' items)',
                'total_quantity' => $otherTotalQuantity,
                'order_count' => $otherOrderCount,
                'total_revenue' => $otherTotalRevenue,
                'is_other_group' => true
            );

            // Add any additional fields that might be present
            if (isset($results[0]['category_id'])) {
                $otherEntry['category_id'] = 'other';
            }
            if (isset($results[0]['product_id'])) {
                $otherEntry['product_id'] = 'other';
            }
            if (isset($results[0]['sku'])) {
                $otherEntry['sku'] = 'other';
            }
            if (isset($results[0]['product_name'])) {
                $otherEntry['product_name'] = 'Other';
            }
            if (isset($results[0]['display_name'])) {
                $otherEntry['display_name'] = 'Other (' . count($otherResults) . ' items)';
            }

            $topResults[] = $otherEntry;
        }

        return $topResults;
    }
}
