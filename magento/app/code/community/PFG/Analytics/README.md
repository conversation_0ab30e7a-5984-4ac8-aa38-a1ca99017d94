# PFG Analytics Module

## Overview
The PFG Analytics module provides comprehensive sales analytics and reporting features for Magento 1.9. It offers detailed insights into order data, status breakdowns, and sales trends with advanced filtering and comparison capabilities.

## Features
- **Sales Analytics Dashboard**: Real-time sales metrics and KPIs
- **Custom Status Mapping**: Group multiple order statuses under custom names
- **Date Range Filtering**: Flexible date filtering with quick preset options
- **Chart Visualization**: Interactive charts with grouping options (daily, weekly, monthly)
- **Comparison Mode**: Period-over-period comparison with percentage changes
- **Status Breakdown**: Detailed breakdown of mapped statuses with pill-style UI
- **Performance Optimized**: SQL aggregation and caching for fast data retrieval
- **Native Magento Styling**: Seamless integration with admin interface

## Installation
1. Copy module files to `app/code/local/PFG/Analytics/`
2. Copy template files to `app/design/adminhtml/default/default/template/pfg/analytics/`
3. Copy layout file to `app/design/adminhtml/default/default/layout/pfg_analytics.xml`
4. Copy CSS file to `skin/adminhtml/default/default/css/pfg/analytics.css`
5. Copy JS file to `js/pfg/analytics.js`
6. Clear cache and refresh admin panel

## Configuration
Navigate to **System > Configuration > PFG > Analytics** to:
- Enable/disable the module
- Configure custom status mappings
- Set priority order for status display

## Usage
Access the analytics dashboard via **PFG > Analytics** in the admin menu.

### Status Mapping
Create custom status names that group multiple real order statuses:
1. Click "Add Status Mapping"
2. Set priority (lower numbers appear first)
3. Enter custom name (e.g., "Completed Orders")
4. Select real statuses to group
5. Save configuration

## Technical Details
- **Version**: 1.2.0
- **Compatibility**: Magento 1.9.x
- **Dependencies**: Mage_Core, Mage_Adminhtml, Mage_Sales
- **Cache**: Automatic cache management with event observers
- **Performance**: Optimized SQL queries with aggregation functions

## File Structure
```
app/code/local/PFG/Analytics/
├── Block/Adminhtml/
├── Helper/Data.php
├── Model/
│   ├── Analytics.php
│   ├── Config.php
│   ├── Observer.php
│   └── System/Config/Backend/
├── controllers/Adminhtml/Pfg/
├── etc/
│   ├── config.xml
│   └── system.xml
└── README.md
```

## Support
For technical support or feature requests, contact the PFG Development Team.

## License
Proprietary - PFG Development Team
